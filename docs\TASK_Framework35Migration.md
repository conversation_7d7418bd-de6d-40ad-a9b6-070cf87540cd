# 项目框架迁移任务分解

## 任务依赖关系图
```mermaid
graph TD
    A[T1: 项目配置修改] --> B[T2: 依赖包清理]
    B --> C[T3: NAudio降级]
    C --> D[T4: 移除皮肤代码]
    D --> E[T5: 重构异步方法]
    E --> F[T6: API兼容性修复]
    F --> G[T7: 编译测试]
    G --> H[T8: 功能验证]
    H --> I[T9: 文档更新]
```

## 原子任务详细定义

### T1: 项目配置修改
**输入契约**:
- 现有的不良品筛选.csproj文件
- 现有的App.config文件
- .NET Framework 3.5要求

**输出契约**:
- 修改后的.csproj文件(TargetFrameworkVersion = v3.5)
- 修改后的App.config文件(supportedRuntime = v2.0)
- 移除IrisSkin4.dll引用

**实现约束**:
- 保持其他项目设置不变
- 保持文件编码格式
- 确保XML格式正确

**验收标准**:
- 项目文件中TargetFrameworkVersion正确设置为v3.5
- App.config中supportedRuntime正确设置
- IrisSkin4引用完全移除
- 项目能够加载到Visual Studio

---

### T2: 依赖包清理
**输入契约**:
- 现有的packages.config文件
- 现有的packages目录
- .NET Framework 3.5兼容性要求

**输出契约**:
- 清理后的packages.config文件
- 移除不兼容包的引用
- 更新.csproj中的引用

**实现约束**:
- 移除Microsoft.Win32.Registry
- 移除System.Security.AccessControl
- 移除System.Security.Principal.Windows
- 保留Microsoft.Office.Interop.Excel

**验收标准**:
- packages.config中只包含兼容.NET 3.5的包
- .csproj中相应引用已移除
- packages目录中不兼容包已清理

---

### T3: NAudio降级
**输入契约**:
- 当前NAudio 2.2.1包
- .NET Framework 3.5兼容性要求
- 现有音频播放功能

**输出契约**:
- NAudio 1.8.5包安装
- 更新的packages.config
- 兼容的NAudio引用

**实现约束**:
- 使用NuGet包管理器降级
- 确保音频播放API兼容
- 保持现有音频文件支持

**验收标准**:
- NAudio版本为1.8.5
- 所有NAudio相关引用正确更新
- 音频播放功能API调用兼容

---

### T4: 移除皮肤代码
**输入契约**:
- Form1.cs中的皮肤相关代码
- Form1.Designer.cs中的skinEngine1控件
- IrisSkin4.dll依赖

**输出契约**:
- 清理后的Form1.cs文件
- 清理后的Form1.Designer.cs文件
- 移除所有皮肤相关代码

**实现约束**:
- 移除skinEngine1控件声明和初始化
- 移除皮肤文件设置代码
- 保持其他UI元素不变

**验收标准**:
- 代码中无任何IrisSkin相关引用
- 窗体正常显示(系统默认样式)
- 编译无皮肤相关错误

---

### T5: 重构异步方法
**输入契约**:
- LoadExcelDataAsync方法
- LoadTextDataAsync方法
- button1_Click中的异步调用
- 现有进度条功能

**输出契约**:
- 基于BackgroundWorker的同步方法
- 重构后的事件处理程序
- 保持进度报告功能

**实现约束**:
- 使用BackgroundWorker替代async/await
- 保持UI响应性
- 保持错误处理机制
- 保持进度条更新

**验收标准**:
- 无async/await关键字
- BackgroundWorker正确实现
- 进度条正常工作
- UI保持响应性
- 错误处理正常

---

### T6: API兼容性修复
**输入契约**:
- 重构后的代码
- .NET Framework 3.5 API限制
- 现有功能要求

**输出契约**:
- .NET 3.5兼容的代码
- 修复所有API兼容性问题
- 保持功能完整性

**实现约束**:
- 替换不兼容的API调用
- 使用.NET 3.5可用的替代方案
- 保持代码逻辑不变

**验收标准**:
- 代码在.NET 3.5下编译通过
- 所有API调用兼容.NET 3.5
- 功能行为保持一致

---

### T7: 编译测试
**输入契约**:
- 修改后的所有源代码文件
- 更新后的项目配置
- .NET Framework 3.5环境

**输出契约**:
- 成功编译的可执行文件
- 无编译错误和警告
- 正确的程序集引用

**实现约束**:
- 在.NET Framework 3.5环境下编译
- 解决所有编译错误
- 优化编译警告

**验收标准**:
- 编译成功，无错误
- 生成正确的可执行文件
- 所有依赖正确解析

---

### T8: 功能验证
**输入契约**:
- 编译成功的应用程序
- 测试用的Excel和文本文件
- 音频文件(pass.mp3, fail.mp3)

**输出契约**:
- 验证报告
- 功能测试结果
- 性能基准数据

**实现约束**:
- 测试所有核心功能
- 验证异常处理
- 检查内存使用

**验收标准**:
- 文件加载功能正常
- 数据搜索功能正常
- 音频播放功能正常
- 进度条显示正常
- 异常处理正确

---

### T9: 文档更新
**输入契约**:
- 完成的迁移项目
- 测试验证结果
- 变更记录

**输出契约**:
- 更新的项目文档
- 迁移总结报告
- 待办事项清单

**实现约束**:
- 记录所有重要变更
- 提供使用说明
- 标注注意事项

**验收标准**:
- 文档完整准确
- 变更记录清晰
- 待办事项明确

## 复杂度评估
- **T1-T3**: 低复杂度 (配置修改)
- **T4**: 中等复杂度 (代码清理)
- **T5**: 高复杂度 (架构重构)
- **T6**: 中等复杂度 (兼容性修复)
- **T7-T9**: 低复杂度 (测试验证)

## 预估时间
总计约2-3小时的开发时间，主要集中在T5异步方法重构任务上。
