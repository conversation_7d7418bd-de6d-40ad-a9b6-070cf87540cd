# 项目框架迁移共识文档

## 最终决策

### 1. 异步处理方案
**决策**: 使用 BackgroundWorker 替代 async/await
**理由**: 保持用户界面响应性，避免大文件加载时界面冻结

### 2. NAudio 版本处理
**决策**: 降级到 NAudio 1.8.5（最后支持.NET 3.5的版本）
**理由**: 确保音频功能在.NET 3.5下正常工作

### 3. 界面外观
**决策**: 接受系统默认样式，移除所有皮肤相关代码
**理由**: 简化依赖，提高兼容性

## 明确的需求描述

### 主要目标
将"不良品筛选工具"从.NET Framework 4.8迁移到.NET Framework 3.5，并移除IrisSkin4.dll依赖。

### 技术实现方案

#### 1. 项目文件修改
- 修改 `不良品筛选.csproj` 中的 `TargetFrameworkVersion` 从 `v4.8` 到 `v3.5`
- 更新 `App.config` 中的 `supportedRuntime` 版本
- 移除 IrisSkin4.dll 引用

#### 2. NuGet包处理
- 移除不兼容.NET 3.5的包：
  - Microsoft.Win32.Registry 4.7.0
  - System.Security.AccessControl 4.7.0  
  - System.Security.Principal.Windows 4.7.0
- 降级NAudio到1.8.5版本
- 保留Microsoft.Office.Interop.Excel（兼容.NET 3.5）

#### 3. 代码修改
- 移除所有IrisSkin相关代码和控件
- 将async/await方法改为BackgroundWorker实现
- 移除不兼容.NET 3.5的API调用
- 保持所有核心业务逻辑不变

### 技术约束
- 必须在.NET Framework 3.5环境下编译和运行
- 保持现有功能完整性
- 不引入新的外部依赖

### 集成方案
- 保持现有的Windows Forms架构
- 保持现有的文件结构和命名空间
- 确保与现有Excel文件格式兼容

## 验收标准

### 功能验收
1. ✅ 文件加载功能正常（Excel和文本文件）
2. ✅ 数据搜索功能正常
3. ✅ 音频播放功能正常（通过/失败提示音）
4. ✅ 进度条显示正常
5. ✅ 状态栏信息更新正常

### 技术验收
1. ✅ 项目在.NET Framework 3.5下成功编译
2. ✅ 应用程序在.NET Framework 3.5环境中正常运行
3. ✅ 完全移除IrisSkin4.dll依赖
4. ✅ 所有NuGet包兼容.NET Framework 3.5
5. ✅ 无运行时错误或异常

### 质量标准
- 代码风格与现有项目保持一致
- 保持良好的错误处理机制
- 用户界面响应性良好
- 内存使用合理，无内存泄漏

## 任务边界限制
- 不修改核心业务逻辑算法
- 不改变用户界面布局和控件位置
- 不添加新功能特性
- 不修改数据文件格式要求

## 确认事项
所有关键假设已基于最佳实践确定：
- 使用BackgroundWorker保证界面响应性
- 降级NAudio确保音频功能可用
- 接受系统默认界面样式
- 移除所有.NET 3.5不兼容的依赖
