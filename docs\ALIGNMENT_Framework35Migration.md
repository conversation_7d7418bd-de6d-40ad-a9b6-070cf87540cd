# 项目框架迁移对齐文档

## 项目上下文分析

### 当前项目状态
- **项目名称**: 不良品筛选工具 (EABU专用筛选工具)
- **当前框架**: .NET Framework 4.8
- **项目类型**: Windows Forms 应用程序
- **主要功能**: Excel/文本文件数据加载与筛选，音频反馈

### 技术栈分析
- **UI框架**: Windows Forms
- **皮肤引擎**: IrisSkin4.dll (Sunisoft.IrisSkin.SkinEngine)
- **Excel操作**: Microsoft.Office.Interop.Excel
- **音频处理**: NAudio 2.2.1
- **其他依赖**: 
  - Microsoft.Win32.Registry 4.7.0
  - System.Security.AccessControl 4.7.0
  - System.Security.Principal.Windows 4.7.0

### 代码结构分析
- **主窗体**: Form1.cs - 包含文件加载、数据搜索、音频播放功能
- **皮肤使用**: 第24行使用 `this.skinEngine1.SkinFile = "WarmColor1.ssk"`
- **设计器**: Form1.Designer.cs - 第37行和第142行定义skinEngine1控件

## 任务需求理解

### 原始需求
用户要求将项目从当前的.NET Framework 4.8更改为.NET Framework 3.5，并移除IrisSkin4.dll依赖。

### 需求分析
1. **框架降级**: .NET Framework 4.8 → 3.5
2. **依赖移除**: 完全移除IrisSkin4.dll及相关代码
3. **功能保持**: 保持核心业务功能不变

### 边界确认
- **包含范围**:
  - 修改项目文件的目标框架版本
  - 更新App.config配置
  - 移除IrisSkin4.dll引用和相关代码
  - 检查并更新NuGet包兼容性
  - 确保项目能在.NET Framework 3.5下正常编译和运行

- **不包含范围**:
  - 不修改核心业务逻辑
  - 不改变用户界面布局和功能
  - 不添加新功能

### 技术约束分析
1. **.NET Framework 3.5限制**:
   - 不支持某些较新的API
   - NuGet包版本兼容性问题
   - 异步编程模型限制

2. **依赖包兼容性**:
   - NAudio 2.2.1可能不兼容.NET 3.5
   - Microsoft.Win32.Registry等包需要检查兼容性
   - Microsoft.Office.Interop.Excel需要验证

### 风险评估
1. **高风险**:
   - NAudio包可能需要降级到兼容.NET 3.5的版本
   - 异步方法(async/await)在.NET 3.5中不可用

2. **中风险**:
   - 某些System命名空间的API可能不可用
   - NuGet包依赖链可能存在兼容性问题

3. **低风险**:
   - Windows Forms基本功能在.NET 3.5中完全支持
   - Excel Interop在.NET 3.5中可用

## 疑问澄清

### 已确认的决策
1. **皮肤移除**: 完全移除IrisSkin4.dll，使用系统默认外观
2. **功能保持**: 保持所有核心功能(文件加载、搜索、音频播放)
3. **框架版本**: 目标为.NET Framework 3.5

### 需要确认的问题
1. **异步处理**: .NET 3.5不支持async/await，需要改为同步处理或使用BackgroundWorker，这样可以接受吗？
2. **NAudio版本**: 可能需要降级NAudio到较旧版本，功能可能有所限制，可以接受吗？
3. **用户体验**: 移除皮肤后界面将使用系统默认样式，外观会有变化，可以接受吗？

## 验收标准
1. 项目成功编译在.NET Framework 3.5环境下
2. 所有核心功能正常工作(文件加载、搜索、音频播放)
3. 完全移除IrisSkin4.dll依赖
4. 应用程序能在.NET Framework 3.5环境中正常运行
5. 不出现运行时错误或异常
