# 项目框架迁移设计文档

## 整体架构设计

### 迁移架构图
```mermaid
graph TB
    A[.NET Framework 4.8项目] --> B[依赖分析]
    B --> C[项目文件修改]
    B --> D[NuGet包处理]
    B --> E[代码重构]
    
    C --> C1[修改csproj文件]
    C --> C2[更新App.config]
    C --> C3[移除IrisSkin引用]
    
    D --> D1[移除不兼容包]
    D --> D2[降级NAudio]
    D --> D3[更新packages.config]
    
    E --> E1[移除皮肤代码]
    E --> E2[重构异步方法]
    E --> E3[API兼容性修复]
    
    C1 --> F[.NET Framework 3.5项目]
    C2 --> F
    C3 --> F
    D1 --> F
    D2 --> F
    D3 --> F
    E1 --> F
    E2 --> F
    E3 --> F
```

## 分层设计

### 1. 项目配置层
**职责**: 管理项目框架版本和依赖配置
- **组件**: 
  - 不良品筛选.csproj
  - App.config
  - packages.config

### 2. 依赖管理层
**职责**: 处理NuGet包和外部依赖
- **移除组件**:
  - IrisSkin4.dll
  - Microsoft.Win32.Registry
  - System.Security.AccessControl
  - System.Security.Principal.Windows
- **保留/降级组件**:
  - NAudio (降级到1.8.5)
  - Microsoft.Office.Interop.Excel

### 3. 用户界面层
**职责**: Windows Forms界面和用户交互
- **修改内容**:
  - 移除skinEngine1控件
  - 保持现有布局和功能
  - 使用系统默认样式

### 4. 业务逻辑层
**职责**: 核心功能实现
- **重构内容**:
  - 异步方法改为BackgroundWorker
  - 保持数据处理逻辑不变
  - 保持音频播放功能

## 核心组件设计

### 1. 文件加载组件重构
```mermaid
graph LR
    A[button1_Click] --> B[ShowProgressBar]
    B --> C[BackgroundWorker.RunWorkerAsync]
    C --> D[DoWork事件处理]
    D --> E[LoadExcelData/LoadTextData]
    E --> F[RunWorkerCompleted事件]
    F --> G[HideProgressBar]
    F --> H[更新UI状态]
```

### 2. 音频播放组件
- **保持**: NAudio基本播放功能
- **修改**: 使用NAudio 1.8.5 API
- **验证**: 确保pass.mp3和fail.mp3正常播放

### 3. 数据搜索组件
- **保持**: 现有搜索逻辑不变
- **确保**: .NET 3.5兼容性

## 接口契约定义

### 1. 文件加载接口
```csharp
// 替换异步方法
private void LoadExcelDataSync(string filePath, BackgroundWorker worker)
private void LoadTextDataSync(string filePath, BackgroundWorker worker)
```

### 2. 进度报告接口
```csharp
// 使用BackgroundWorker进度报告
worker.ReportProgress(percentage, userState)
```

## 数据流向图
```mermaid
graph TD
    A[用户选择文件] --> B[显示进度条]
    B --> C[BackgroundWorker开始]
    C --> D[读取文件数据]
    D --> E[报告进度]
    E --> F[更新进度条]
    F --> D
    D --> G[数据加载完成]
    G --> H[隐藏进度条]
    H --> I[更新状态显示]
```

## 异常处理策略

### 1. 文件加载异常
- **捕获**: IOException, COMException
- **处理**: 显示错误消息，重置UI状态
- **恢复**: 隐藏进度条，清理资源

### 2. 音频播放异常
- **捕获**: NAudio相关异常
- **处理**: 静默处理，不影响主要功能
- **日志**: 记录错误信息

### 3. 依赖缺失异常
- **检测**: 启动时检查关键依赖
- **处理**: 提供明确的错误信息
- **指导**: 提示用户安装必要组件

## 兼容性保证

### .NET Framework 3.5 API限制
1. **不可用API**:
   - async/await 关键字
   - Task类 (System.Threading.Tasks)
   - 某些LINQ扩展方法

2. **替代方案**:
   - BackgroundWorker替代async/await
   - 传统委托替代Task
   - 基础LINQ方法仍可用

### 向后兼容性
- 保持所有公共接口不变
- 保持文件格式兼容性
- 保持用户操作流程不变

## 性能考虑

### 1. 内存使用
- 及时释放COM对象(Excel Interop)
- 正确处置音频资源
- 避免大数组长期占用内存

### 2. 响应性
- 使用BackgroundWorker保持UI响应
- 适当的进度报告频率
- 避免UI线程阻塞

## 测试策略

### 1. 单元测试
- 数据加载功能测试
- 搜索算法测试
- 音频播放测试

### 2. 集成测试
- 完整工作流程测试
- 异常情况处理测试
- 性能基准测试

### 3. 兼容性测试
- .NET Framework 3.5环境测试
- 不同Windows版本测试
- 各种Excel文件格式测试
