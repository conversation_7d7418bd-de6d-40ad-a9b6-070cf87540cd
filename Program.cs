﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Threading;

namespace 不良品筛选
{
    internal static class Program
    {
        // 定义一个全局互斥体名称
        private static Mutex mutex = new Mutex(true, "不良品筛选_Application");

        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main()
        {
            if (mutex.WaitOne(TimeSpan.Zero, true))
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                Application.Run(new Form1());
                mutex.ReleaseMutex();
            }
            else
            {
                // 如果互斥体已被锁定，则说明应用程序已经在运行中
                MessageBox.Show("应用程序已经在运行中！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }
    }
}
