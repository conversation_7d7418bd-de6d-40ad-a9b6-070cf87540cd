﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Windows.Forms;
using Excel = Microsoft.Office.Interop.Excel;
using System.Media; // 使用System.Media进行音频播放

namespace 不良品筛选
{
    public partial class Form1 : Form
    {
        private SoundPlayer soundPlayer;
        private string[,] excelData; // 添加一个私有字段用于存储 Excel 数据
        private ProgressBar progressBar; // 添加进度条控件
        private int dataCount = 0; // 记录加载的数据条目数

        public Form1()
        {
            InitializeComponent();
            soundPlayer = new SoundPlayer(); // 初始化音频播放器
            textBox1.KeyDown += TextBox1_KeyDown; // 将 textBox1 的 KeyDown 事件与事件处理程序关联

            // 初始化进度条控件
            progressBar = new ProgressBar();
            progressBar.Style = ProgressBarStyle.Continuous;
            progressBar.Minimum = 0;
            progressBar.Maximum = 100;
            progressBar.Visible = false;
            Controls.Add(progressBar);
        }

        private void TextBox1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                PerformSearch();
            }
        }

        private void button1_Click(object sender, EventArgs e)
        {
            OpenFileDialog openFileDialog = new OpenFileDialog();
            openFileDialog.Filter = "Excel Files|*.txt;*.xls;*.xlsx|Text Files|*.xlsm|All Files|*.*";
            openFileDialog.Title = "Select Excel or Text File";

            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                string filePath = openFileDialog.FileName;
                ShowProgressBar(); // 显示进度条

                // 重置数据计数
                dataCount = 0;

                // 创建BackgroundWorker
                BackgroundWorker worker = new BackgroundWorker();
                worker.WorkerReportsProgress = true;
                worker.DoWork += (s, args) =>
                {
                    try
                    {
                        if (Path.GetExtension(filePath).ToLower() == ".txt")
                        {
                            LoadTextDataSync(filePath, worker);
                        }
                        else
                        {
                            LoadExcelDataSync(filePath, worker);
                        }
                        args.Result = "success";
                    }
                    catch (Exception ex)
                    {
                        args.Result = ex.Message;
                    }
                };

                worker.ProgressChanged += (s, args) =>
                {
                    // 更新进度条
                    progressBar.Value = args.ProgressPercentage;
                };

                worker.RunWorkerCompleted += (s, args) =>
                {
                    HideProgressBar(); // 隐藏进度条

                    if (args.Result.ToString() == "success")
                    {
                        label1.Text = "加载成功";
                        label1.BackColor = Color.Yellow; // 设置背景为黄色
                        toolStripStatusLabel1.Text = string.Format("已加载数据条目：{0} pcs", dataCount); // 更新状态栏显示
                        toolStripStatusLabel1.BackColor = Color.Green; // 设置背景为绿色
                    }
                    else
                    {
                        MessageBox.Show(string.Format("加载文件时出错：{0}", args.Result), "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                };

                worker.RunWorkerAsync();
            }
        }

        private void LoadExcelDataSync(string filePath, BackgroundWorker worker)
        {
            Excel.Application excelApp = new Excel.Application();
            Excel.Workbook workbook = excelApp.Workbooks.Open(filePath);
            Excel.Worksheet worksheet = (Excel.Worksheet)workbook.Sheets[1]; // 假设数据在第一个工作表中

            Excel.Range range = worksheet.UsedRange;
            int rowCount = range.Rows.Count;
            int colCount = range.Columns.Count;

            excelData = new string[rowCount, colCount];

            int totalCells = rowCount * colCount;
            int processedCells = 0;

            // 将 Excel 中的数据存储到数组中，跳过空白单元格
            for (int i = 1; i <= rowCount; i++)
            {
                for (int j = 1; j <= colCount; j++)
                {
                    object cellValue = ((Excel.Range)range.Cells[i, j]).Value2;
                    if (cellValue != null)
                    {
                        excelData[i - 1, j - 1] = cellValue.ToString();
                    }
                    else
                    {
                        excelData[i - 1, j - 1] = "";
                    }

                    processedCells++;
                    dataCount++; // 更新数据加载计数

                    // 报告进度
                    if (processedCells % 100 == 0 || processedCells == totalCells)
                    {
                        int percentage = (int)((double)processedCells / totalCells * 100);
                        worker.ReportProgress(percentage);
                    }
                }
            }

            workbook.Close(false);
            excelApp.Quit();

            System.Runtime.InteropServices.Marshal.ReleaseComObject(worksheet);
            System.Runtime.InteropServices.Marshal.ReleaseComObject(workbook);
            System.Runtime.InteropServices.Marshal.ReleaseComObject(excelApp);
        }

        private void LoadTextDataSync(string filePath, BackgroundWorker worker)
        {
            // 读取文本文件的数据
            string[] lines = File.ReadAllLines(filePath);

            // 确定数组大小
            int rowCount = lines.Length;
            int colCount = lines[0].Split('\t').Length; // 假设以制表符分隔列

            excelData = new string[rowCount, colCount];

            int totalCells = rowCount * colCount;
            int processedCells = 0;

            // 将文本数据存储到数组中
            for (int i = 0; i < rowCount; i++)
            {
                string[] lineValues = lines[i].Split('\t');
                for (int j = 0; j < colCount; j++)
                {
                    excelData[i, j] = lineValues[j];

                    processedCells++;
                    dataCount++; // 更新数据加载计数

                    // 报告进度
                    if (processedCells % 100 == 0 || processedCells == totalCells)
                    {
                        int percentage = (int)((double)processedCells / totalCells * 100);
                        worker.ReportProgress(percentage);
                    }
                }
            }
        }

        private void ShowProgressBar()
        {
            progressBar.Visible = true;
            progressBar.Location = new Point(label1.Right - progressBar.Width, label1.Bottom + 5);
        }

        private void HideProgressBar()
        {
            progressBar.Visible = false;
        }

        private void PerformSearch()
        {
            if (excelData == null)
            {
                MessageBox.Show("请先加载文件", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            string searchText = textBox1.Text.Trim();
            bool found = false;

            int rowCount = excelData.GetLength(0);
            int colCount = excelData.GetLength(1);

            for (int i = 0; i < rowCount; i++)
            {
                if (excelData[i, 0] == searchText) // 假设搜索第一列数据
                {
                    found = true;
                    break;
                }
            }

            if (found)
            {
                label1.Text = "Fail";
                label1.BackColor = Color.Red; // 设置背景为红色
                PlayFailSound();
                textBox1.SelectAll(); // 如果存在，则全选 textBox1 内容
            }
            else
            {
                label1.Text = "PASS";
                label1.BackColor = Color.Green; // 设置背景为绿色
                PlayPassSound();
                textBox1.Clear(); // 如果不存在，则清空 textBox1 内容
            }
        }

        private void PlayAudio(string audioFileName)
        {
            try
            {
                string audioFilePath = Path.Combine(Path.Combine(Application.StartupPath, "Properties"), audioFileName);
                if (File.Exists(audioFilePath))
                {
                    // 对于MP3文件，使用系统默认播放器
                    if (Path.GetExtension(audioFileName).ToLower() == ".mp3")
                    {
                        System.Diagnostics.Process.Start(audioFilePath);
                    }
                    else
                    {
                        // 对于WAV文件，使用SoundPlayer
                        soundPlayer.SoundLocation = audioFilePath;
                        soundPlayer.Play();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(string.Format("播放音频文件时出错：{0}", ex.Message), "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void PlayPassSound()
        {
            PlayAudio("pass.mp3");
        }

        private void PlayFailSound()
        {
            PlayAudio("fail.mp3");
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            if (soundPlayer != null)
                soundPlayer.Dispose();

            base.OnFormClosing(e);
        }

        private void toolStripStatusLabel1_Click(object sender, EventArgs e)
        {

        }
    }
}
