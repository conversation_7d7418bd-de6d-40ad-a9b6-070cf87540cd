﻿using System;
using System.Drawing;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Forms;
using Excel = Microsoft.Office.Interop.Excel;
using NAudio.Wave; // 将 using NAudio.Wave; 移动到命名空间声明之前

namespace 不良品筛选
{
    public partial class Form1 : Form
    {
        private WaveOutEvent outputDevice;
        private AudioFileReader audioFile;
        private string[,] excelData; // 添加一个私有字段用于存储 Excel 数据
        private ProgressBar progressBar; // 添加进度条控件
        private int dataCount = 0; // 记录加载的数据条目数

        public Form1()
        {
            InitializeComponent();
            outputDevice = new WaveOutEvent(); // 初始化播放设备
            textBox1.KeyDown += TextBox1_KeyDown; // 将 textBox1 的 KeyDown 事件与事件处理程序关联
            this.skinEngine1.SkinFile = "WarmColor1.ssk"; 
            // 初始化进度条控件
            progressBar = new ProgressBar();
            progressBar.Style = ProgressBarStyle.Marquee;
            progressBar.Visible = false;
            Controls.Add(progressBar);
        }

        private void TextBox1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                PerformSearch();
            }
        }

        private async void button1_Click(object sender, EventArgs e)
        {
            OpenFileDialog openFileDialog = new OpenFileDialog();
            openFileDialog.Filter = "Excel Files|*.txt;*.xls;*.xlsx|Text Files|*.xlsm|All Files|*.*";
            openFileDialog.Title = "Select Excel or Text File";

            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                string filePath = openFileDialog.FileName;
                ShowProgressBar(); // 显示进度条

                // 重置数据计数
                dataCount = 0;

                try
                {
                    if (Path.GetExtension(filePath).ToLower() == ".txt")
                    {
                        await LoadTextDataAsync(filePath);
                    }
                    else
                    {
                        await LoadExcelDataAsync(filePath);
                    }

                    label1.Text = "加载成功";
                    label1.BackColor = Color.Yellow; // 设置背景为黄色
                    toolStripStatusLabel1.Text = $"已加载数据条目：{dataCount} pcs"; // 更新状态栏显示
                    toolStripStatusLabel1.BackColor = Color.Green; // 设置背景为绿色
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"加载文件时出错：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }

                HideProgressBar(); // 隐藏进度条
            }
        }

        private async Task LoadExcelDataAsync(string filePath)
        {
            await Task.Run(() =>
            {
                Excel.Application excelApp = new Excel.Application();
                Excel.Workbook workbook = excelApp.Workbooks.Open(filePath);
                Excel.Worksheet worksheet = workbook.Sheets[1]; // 假设数据在第一个工作表中

                Excel.Range range = worksheet.UsedRange;
                int rowCount = range.Rows.Count;
                int colCount = range.Columns.Count;

                excelData = new string[rowCount, colCount];

                // 更新进度条状态
                progressBar.Maximum = rowCount * colCount;
                progressBar.Value = 0;

                // 将 Excel 中的数据存储到数组中，跳过空白单元格
                for (int i = 1; i <= rowCount; i++)
                {
                    for (int j = 1; j <= colCount; j++)
                    {
                        if (range.Cells[i, j].Value2 != null)
                        {
                            excelData[i - 1, j - 1] = range.Cells[i, j].Value2.ToString();
                        }
                        else
                        {
                            excelData[i - 1, j - 1] = "";
                        }

                        progressBar.Value++; // 更新进度条
                        dataCount++; // 更新数据加载计数
                    }
                }

                workbook.Close(false);
                excelApp.Quit();

                System.Runtime.InteropServices.Marshal.ReleaseComObject(worksheet);
                System.Runtime.InteropServices.Marshal.ReleaseComObject(workbook);
                System.Runtime.InteropServices.Marshal.ReleaseComObject(excelApp);
            });
        }

        private async Task LoadTextDataAsync(string filePath)
        {
            await Task.Run(() =>
            {
                // 读取文本文件的数据
                string[] lines = File.ReadAllLines(filePath);

                // 确定数组大小
                int rowCount = lines.Length;
                int colCount = lines[0].Split('\t').Length; // 假设以制表符分隔列

                excelData = new string[rowCount, colCount];

                // 更新进度条状态
                progressBar.Maximum = rowCount * colCount;
                progressBar.Value = 0;

                // 将文本数据存储到数组中
                for (int i = 0; i < rowCount; i++)
                {
                    string[] lineValues = lines[i].Split('\t');
                    for (int j = 0; j < colCount; j++)
                    {
                        excelData[i, j] = lineValues[j];

                        progressBar.Value++; // 更新进度条
                        dataCount++; // 更新数据加载计数
                    }
                }
            });
        }

        private void ShowProgressBar()
        {
            progressBar.Visible = true;
            progressBar.Location = new Point(label1.Right - progressBar.Width, label1.Bottom + 5);
        }

        private void HideProgressBar()
        {
            progressBar.Visible = false;
        }

        private void PerformSearch()
        {
            if (excelData == null)
            {
                MessageBox.Show("请先加载文件", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            string searchText = textBox1.Text.Trim();
            bool found = false;

            int rowCount = excelData.GetLength(0);
            int colCount = excelData.GetLength(1);

            for (int i = 0; i < rowCount; i++)
            {
                if (excelData[i, 0] == searchText) // 假设搜索第一列数据
                {
                    found = true;
                    break;
                }
            }

            if (found)
            {
                label1.Text = "Fail";
                label1.BackColor = Color.Red; // 设置背景为红色
                PlayFailSound();
                textBox1.SelectAll(); // 如果存在，则全选 textBox1 内容
            }
            else
            {
                label1.Text = "PASS";
                label1.BackColor = Color.Green; // 设置背景为绿色
                PlayPassSound();
                textBox1.Clear(); // 如果不存在，则清空 textBox1 内容
            }
        }

        private void PlayMP3(string mp3FileName)
        {
            try
            {
                outputDevice.Stop();

                string mp3FilePath = Path.Combine(Application.StartupPath, "Properties", mp3FileName);
                audioFile = new AudioFileReader(mp3FilePath);
                outputDevice.Init(audioFile);
                outputDevice.Play();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"播放音频文件时出错：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void PlayPassSound()
        {
            PlayMP3("pass.mp3");
        }

        private void PlayFailSound()
        {
            PlayMP3("fail.mp3");
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            outputDevice.Dispose();
            if (audioFile != null)
                audioFile.Dispose();

            base.OnFormClosing(e);
        }

        private void toolStripStatusLabel1_Click(object sender, EventArgs e)
        {

        }
    }
}
