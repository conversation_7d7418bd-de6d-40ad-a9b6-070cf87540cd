<?xml version="1.0"?>
<doc>
    <assembly>
        <name>NAudio.Asio</name>
    </assembly>
    <members>
        <member name="T:NAudio.Wave.Asio.Asio64Bit">
            <summary>
            ASIO 64 bit value
            Unfortunately the ASIO API was implemented it before compiler supported consistently 64 bit 
            integer types. By using the structure the data layout on a little-endian system like the 
            Intel x86 architecture will result in a "non native" storage of the 64 bit data. The most 
            significant 32 bit are stored first in memory, the least significant bits are stored in the 
            higher memory space. However each 32 bit is stored in the native little-endian fashion
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.Asio64Bit.hi">
            <summary>
            most significant bits (Bits 32..63)
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.Asio64Bit.lo">
            <summary>
            least significant bits (Bits 0..31)
            </summary>
        </member>
        <member name="T:NAudio.Wave.Asio.AsioCallbacks">
            <summary>
            ASIO Callbacks
            </summary>
        </member>
        <member name="T:NAudio.Wave.Asio.AsioCallbacks.AsioBufferSwitchCallBack">
            <summary>
            ASIO Buffer Switch Callback
            </summary>
        </member>
        <member name="T:NAudio.Wave.Asio.AsioCallbacks.AsioSampleRateDidChangeCallBack">
            <summary>
            ASIO Sample Rate Did Change Callback
            </summary>
        </member>
        <member name="T:NAudio.Wave.Asio.AsioCallbacks.AsioAsioMessageCallBack">
            <summary>
            ASIO Message Callback
            </summary>
        </member>
        <member name="T:NAudio.Wave.Asio.AsioCallbacks.AsioBufferSwitchTimeInfoCallBack">
            <summary>
            ASIO Buffer Switch Time Info Callback
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioCallbacks.pbufferSwitch">
            <summary>
            Buffer switch callback
            void (*bufferSwitch) (long doubleBufferIndex, AsioBool directProcess);
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioCallbacks.psampleRateDidChange">
            <summary>
            Sample Rate Changed callback
            void (*sampleRateDidChange) (AsioSampleRate sRate);
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioCallbacks.pasioMessage">
            <summary>
            ASIO Message callback
            long (*asioMessage) (long selector, long value, void* message, double* opt);
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioCallbacks.pbufferSwitchTimeInfo">
            <summary>
            ASIO Buffer Switch Time Info Callback
            AsioTime* (*bufferSwitchTimeInfo) (AsioTime* params, long doubleBufferIndex, AsioBool directProcess);
            </summary>
        </member>
        <member name="T:NAudio.Wave.Asio.AsioChannelInfo">
            <summary>
            ASIO Channel Info
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioChannelInfo.channel">
            <summary>
            on input, channel index
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioChannelInfo.isInput">
            <summary>
            Is Input
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioChannelInfo.isActive">
            <summary>
            Is Active
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioChannelInfo.channelGroup">
            <summary>
            Channel Info
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioChannelInfo.type">
            <summary>
            ASIO Sample Type
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioChannelInfo.name">
            <summary>
            Name
            </summary>
        </member>
        <member name="T:NAudio.Wave.Asio.AsioDriver">
            <summary>
            Main AsioDriver Class. To use this class, you need to query first the GetAsioDriverNames() and
            then use the GetAsioDriverByName to instantiate the correct AsioDriver.
            This is the first AsioDriver binding fully implemented in C#!
            
            Contributor: Alexandre Mutel - email: alexandre_mutel at yahoo.fr
            </summary>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriver.GetAsioDriverNames">
            <summary>
            Gets the ASIO driver names installed.
            </summary>
            <returns>a list of driver names. Use this name to GetAsioDriverByName</returns>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriver.GetAsioDriverByName(System.String)">
            <summary>
            Instantiate a AsioDriver given its name.
            </summary>
            <param name="name">The name of the driver</param>
            <returns>an AsioDriver instance</returns>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriver.GetAsioDriverByGuid(System.Guid)">
            <summary>
            Instantiate the ASIO driver by GUID.
            </summary>
            <param name="guid">The GUID.</param>
            <returns>an AsioDriver instance</returns>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriver.Init(System.IntPtr)">
            <summary>
            Inits the AsioDriver..
            </summary>
            <param name="sysHandle">The sys handle.</param>
            <returns></returns>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriver.GetDriverName">
            <summary>
            Gets the name of the driver.
            </summary>
            <returns></returns>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriver.GetDriverVersion">
            <summary>
            Gets the driver version.
            </summary>
            <returns></returns>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriver.GetErrorMessage">
            <summary>
            Gets the error message.
            </summary>
            <returns></returns>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriver.Start">
            <summary>
            Starts this instance.
            </summary>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriver.Stop">
            <summary>
            Stops this instance.
            </summary>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriver.GetChannels(System.Int32@,System.Int32@)">
            <summary>
            Gets the number of channels.
            </summary>
            <param name="numInputChannels">The num input channels.</param>
            <param name="numOutputChannels">The num output channels.</param>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriver.GetLatencies(System.Int32@,System.Int32@)">
            <summary>
            Gets the latencies (n.b. does not throw an exception)
            </summary>
            <param name="inputLatency">The input latency.</param>
            <param name="outputLatency">The output latency.</param>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriver.GetBufferSize(System.Int32@,System.Int32@,System.Int32@,System.Int32@)">
            <summary>
            Gets the size of the buffer.
            </summary>
            <param name="minSize">Size of the min.</param>
            <param name="maxSize">Size of the max.</param>
            <param name="preferredSize">Size of the preferred.</param>
            <param name="granularity">The granularity.</param>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriver.CanSampleRate(System.Double)">
            <summary>
            Determines whether this instance can use the specified sample rate.
            </summary>
            <param name="sampleRate">The sample rate.</param>
            <returns>
            	<c>true</c> if this instance [can sample rate] the specified sample rate; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriver.GetSampleRate">
            <summary>
            Gets the sample rate.
            </summary>
            <returns></returns>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriver.SetSampleRate(System.Double)">
            <summary>
            Sets the sample rate.
            </summary>
            <param name="sampleRate">The sample rate.</param>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriver.GetClockSources(System.Int64@,System.Int32)">
            <summary>
            Gets the clock sources.
            </summary>
            <param name="clocks">The clocks.</param>
            <param name="numSources">The num sources.</param>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriver.SetClockSource(System.Int32)">
            <summary>
            Sets the clock source.
            </summary>
            <param name="reference">The reference.</param>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriver.GetSamplePosition(System.Int64@,NAudio.Wave.Asio.Asio64Bit@)">
            <summary>
            Gets the sample position.
            </summary>
            <param name="samplePos">The sample pos.</param>
            <param name="timeStamp">The time stamp.</param>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriver.GetChannelInfo(System.Int32,System.Boolean)">
            <summary>
            Gets the channel info.
            </summary>
            <param name="channelNumber">The channel number.</param>
            <param name="trueForInputInfo">if set to <c>true</c> [true for input info].</param>
            <returns>Channel Info</returns>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriver.CreateBuffers(System.IntPtr,System.Int32,System.Int32,NAudio.Wave.Asio.AsioCallbacks@)">
            <summary>
            Creates the buffers.
            </summary>
            <param name="bufferInfos">The buffer infos.</param>
            <param name="numChannels">The num channels.</param>
            <param name="bufferSize">Size of the buffer.</param>
            <param name="callbacks">The callbacks.</param>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriver.DisposeBuffers">
            <summary>
            Disposes the buffers.
            </summary>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriver.ControlPanel">
            <summary>
            Controls the panel.
            </summary>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriver.Future(System.Int32,System.IntPtr)">
            <summary>
            Futures the specified selector.
            </summary>
            <param name="selector">The selector.</param>
            <param name="opt">The opt.</param>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriver.OutputReady">
            <summary>
            Notifies OutputReady to the AsioDriver.
            </summary>
            <returns></returns>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriver.ReleaseComAsioDriver">
            <summary>
            Releases this instance.
            </summary>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriver.HandleException(NAudio.Wave.Asio.AsioError,System.String)">
            <summary>
            Handles the exception. Throws an exception based on the error.
            </summary>
            <param name="error">The error to check.</param>
            <param name="methodName">Method name</param>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriver.InitFromGuid(System.Guid)">
            <summary>
            Inits the vTable method from GUID. This is a tricky part of this class.
            </summary>
            <param name="asioGuid">The ASIO GUID.</param>
        </member>
        <member name="T:NAudio.Wave.Asio.AsioDriver.AsioDriverVTable">
            <summary>
            Internal VTable structure to store all the delegates to the C++ COM method.
            </summary>
        </member>
        <member name="T:NAudio.Wave.Asio.AsioDriverCapability">
            <summary>
            AsioDriverCapability holds all the information from the AsioDriver.
            Use AsioDriverExt to get the Capabilities
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioDriverCapability.DriverName">
            <summary>
            Drive Name
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioDriverCapability.NbInputChannels">
            <summary>
            Number of Input Channels
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioDriverCapability.NbOutputChannels">
            <summary>
            Number of Output Channels
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioDriverCapability.InputLatency">
            <summary>
            Input Latency
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioDriverCapability.OutputLatency">
            <summary>
            Output Latency
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioDriverCapability.BufferMinSize">
            <summary>
            Buffer Minimum Size
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioDriverCapability.BufferMaxSize">
            <summary>
            Buffer Maximum Size
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioDriverCapability.BufferPreferredSize">
            <summary>
            Buffer Preferred Size
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioDriverCapability.BufferGranularity">
            <summary>
            Buffer Granularity
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioDriverCapability.SampleRate">
            <summary>
            Sample Rate
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioDriverCapability.InputChannelInfos">
            <summary>
            Input Channel Info
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioDriverCapability.OutputChannelInfos">
            <summary>
            Output Channel Info
            </summary>
        </member>
        <member name="T:NAudio.Wave.Asio.AsioFillBufferCallback">
            <summary>
            Callback used by the AsioDriverExt to get wave data
            </summary>
        </member>
        <member name="T:NAudio.Wave.Asio.AsioDriverExt">
            <summary>
            AsioDriverExt is a simplified version of the AsioDriver. It provides an easier
            way to access the capabilities of the Driver and implement the callbacks necessary 
            for feeding the driver.
            Implementation inspired from Rob Philpot's with a managed C++ ASIO wrapper BlueWave.Interop.Asio
            http://www.codeproject.com/KB/mcpp/Asio.Net.aspx
            
            Contributor: Alexandre Mutel - email: alexandre_mutel at yahoo.fr
            </summary>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriverExt.#ctor(NAudio.Wave.Asio.AsioDriver)">
            <summary>
            Initializes a new instance of the <see cref="T:NAudio.Wave.Asio.AsioDriverExt"/> class based on an already
            instantiated AsioDriver instance.
            </summary>
            <param name="driver">A AsioDriver already instantiated.</param>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriverExt.SetChannelOffset(System.Int32,System.Int32)">
            <summary>
            Allows adjustment of which is the first output channel we write to
            </summary>
            <param name="outputChannelOffset">Output Channel offset</param>
            <param name="inputChannelOffset">Input Channel offset</param>
        </member>
        <member name="P:NAudio.Wave.Asio.AsioDriverExt.Driver">
            <summary>
            Gets the driver used.
            </summary>
            <value>The ASIOdriver.</value>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriverExt.Start">
            <summary>
            Starts playing the buffers.
            </summary>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriverExt.Stop">
            <summary>
            Stops playing the buffers.
            </summary>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriverExt.ShowControlPanel">
            <summary>
            Shows the control panel.
            </summary>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriverExt.ReleaseDriver">
            <summary>
            Releases this instance.
            </summary>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriverExt.IsSampleRateSupported(System.Double)">
            <summary>
            Determines whether the specified sample rate is supported.
            </summary>
            <param name="sampleRate">The sample rate.</param>
            <returns>
            	<c>true</c> if [is sample rate supported]; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriverExt.SetSampleRate(System.Double)">
            <summary>
            Sets the sample rate.
            </summary>
            <param name="sampleRate">The sample rate.</param>
        </member>
        <member name="P:NAudio.Wave.Asio.AsioDriverExt.FillBufferCallback">
            <summary>
            Gets or sets the fill buffer callback.
            </summary>
            <value>The fill buffer callback.</value>
        </member>
        <member name="P:NAudio.Wave.Asio.AsioDriverExt.Capabilities">
            <summary>
            Gets the capabilities of the AsioDriver.
            </summary>
            <value>The capabilities.</value>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriverExt.CreateBuffers(System.Int32,System.Int32,System.Boolean)">
            <summary>
            Creates the buffers for playing.
            </summary>
            <param name="numberOfOutputChannels">The number of outputs channels.</param>
            <param name="numberOfInputChannels">The number of input channel.</param>
            <param name="useMaxBufferSize">if set to <c>true</c> [use max buffer size] else use Prefered size</param>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriverExt.BuildCapabilities">
            <summary>
            Builds the capabilities internally.
            </summary>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriverExt.BufferSwitchCallBack(System.Int32,System.Boolean)">
            <summary>
            Callback called by the AsioDriver on fill buffer demand. Redirect call to external callback.
            </summary>
            <param name="doubleBufferIndex">Index of the double buffer.</param>
            <param name="directProcess">if set to <c>true</c> [direct process].</param>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriverExt.SampleRateDidChangeCallBack(System.Double)">
            <summary>
            Callback called by the AsioDriver on event "Samples rate changed".
            </summary>
            <param name="sRate">The sample rate.</param>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriverExt.AsioMessageCallBack(NAudio.Wave.Asio.AsioMessageSelector,System.Int32,System.IntPtr,System.IntPtr)">
            <summary>
            Asio message call back.
            </summary>
            <param name="selector">The selector.</param>
            <param name="value">The value.</param>
            <param name="message">The message.</param>
            <param name="opt">The opt.</param>
            <returns></returns>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioDriverExt.BufferSwitchTimeInfoCallBack(System.IntPtr,System.Int32,System.Boolean)">
            <summary>
            Buffers switch time info call back.
            </summary>
            <param name="asioTimeParam">The asio time param.</param>
            <param name="doubleBufferIndex">Index of the double buffer.</param>
            <param name="directProcess">if set to <c>true</c> [direct process].</param>
            <returns></returns>
        </member>
        <member name="T:NAudio.Wave.Asio.AsioError">
            <summary>
            ASIO Error Codes
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioError.ASE_OK">
            <summary>
            This value will be returned whenever the call succeeded
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioError.ASE_SUCCESS">
            <summary>
            unique success return value for ASIOFuture calls
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioError.ASE_NotPresent">
            <summary>
            hardware input or output is not present or available
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioError.ASE_HWMalfunction">
            <summary>
            hardware is malfunctioning (can be returned by any ASIO function)
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioError.ASE_InvalidParameter">
            <summary>
            input parameter invalid
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioError.ASE_InvalidMode">
            <summary>
            hardware is in a bad mode or used in a bad mode
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioError.ASE_SPNotAdvancing">
            <summary>
            hardware is not running when sample position is inquired
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioError.ASE_NoClock">
            <summary>
            sample clock or rate cannot be determined or is not present
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioError.ASE_NoMemory">
            <summary>
            not enough memory for completing the request
            </summary>
        </member>
        <member name="T:NAudio.Wave.Asio.AsioMessageSelector">
            <summary>
            ASIO Message Selector
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioMessageSelector.kAsioSelectorSupported">
            <summary>
            selector in &lt;value&gt;, returns 1L if supported,
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioMessageSelector.kAsioEngineVersion">
            <summary>
            returns engine (host) asio implementation version,
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioMessageSelector.kAsioResetRequest">
            <summary>
            request driver reset. if accepted, this
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioMessageSelector.kAsioBufferSizeChange">
            <summary>
            not yet supported, will currently always return 0L.
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioMessageSelector.kAsioResyncRequest">
            <summary>
            the driver went out of sync, such that
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioMessageSelector.kAsioLatenciesChanged">
            <summary>
            the drivers latencies have changed. The engine
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioMessageSelector.kAsioSupportsTimeInfo">
            <summary>
            if host returns true here, it will expect the
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioMessageSelector.kAsioSupportsTimeCode">
            <summary>
            supports timecode
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioMessageSelector.kAsioMMCCommand">
            <summary>
            unused - value: number of commands, message points to mmc commands
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioMessageSelector.kAsioSupportsInputMonitor">
            <summary>
            kAsioSupportsXXX return 1 if host supports this
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioMessageSelector.kAsioSupportsInputGain">
            <summary>
            unused and undefined
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioMessageSelector.kAsioSupportsInputMeter">
            <summary>
            unused and undefined
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioMessageSelector.kAsioSupportsOutputGain">
            <summary>
            unused and undefined
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioMessageSelector.kAsioSupportsOutputMeter">
            <summary>
            unused and undefined
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioMessageSelector.kAsioOverload">
            <summary>
            driver detected an overload
            </summary>
        </member>
        <member name="T:NAudio.Wave.Asio.AsioSampleConvertor">
            <summary>
            This class stores convertors for different interleaved WaveFormat to ASIOSampleType separate channel
            format.
            </summary>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioSampleConvertor.SelectSampleConvertor(NAudio.Wave.WaveFormat,NAudio.Wave.Asio.AsioSampleType)">
            <summary>
            Selects the sample convertor based on the input WaveFormat and the output ASIOSampleTtype.
            </summary>
            <param name="waveFormat">The wave format.</param>
            <param name="asioType">The type.</param>
            <returns></returns>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioSampleConvertor.ConvertorShortToInt2Channels(System.IntPtr,System.IntPtr[],System.Int32,System.Int32)">
            <summary>
            Optimized convertor for 2 channels SHORT
            </summary>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioSampleConvertor.ConvertorShortToIntGeneric(System.IntPtr,System.IntPtr[],System.Int32,System.Int32)">
            <summary>
            Generic convertor for SHORT
            </summary>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioSampleConvertor.ConvertorFloatToInt2Channels(System.IntPtr,System.IntPtr[],System.Int32,System.Int32)">
            <summary>
            Optimized convertor for 2 channels FLOAT
            </summary>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioSampleConvertor.ConvertorFloatToIntGeneric(System.IntPtr,System.IntPtr[],System.Int32,System.Int32)">
            <summary>
            Generic convertor Float to INT
            </summary>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioSampleConvertor.ConvertorIntToInt2Channels(System.IntPtr,System.IntPtr[],System.Int32,System.Int32)">
            <summary>
            Optimized convertor for 2 channels INT to INT
            </summary>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioSampleConvertor.ConvertorIntToIntGeneric(System.IntPtr,System.IntPtr[],System.Int32,System.Int32)">
            <summary>
            Generic convertor INT to INT
            </summary>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioSampleConvertor.ConvertorIntToShort2Channels(System.IntPtr,System.IntPtr[],System.Int32,System.Int32)">
            <summary>
            Optimized convertor for 2 channels INT to SHORT
            </summary>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioSampleConvertor.ConvertorIntToShortGeneric(System.IntPtr,System.IntPtr[],System.Int32,System.Int32)">
            <summary>
            Generic convertor INT to SHORT
            </summary>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioSampleConvertor.ConvertorIntToFloatGeneric(System.IntPtr,System.IntPtr[],System.Int32,System.Int32)">
            <summary>
            Generic convertor INT to FLOAT
            </summary>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioSampleConvertor.ConvertorShortToShort2Channels(System.IntPtr,System.IntPtr[],System.Int32,System.Int32)">
            <summary>
            Optimized convertor for 2 channels SHORT
            </summary>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioSampleConvertor.ConvertorShortToShortGeneric(System.IntPtr,System.IntPtr[],System.Int32,System.Int32)">
            <summary>
            Generic convertor for SHORT
            </summary>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioSampleConvertor.ConvertorFloatToShort2Channels(System.IntPtr,System.IntPtr[],System.Int32,System.Int32)">
            <summary>
            Optimized convertor for 2 channels FLOAT
            </summary>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioSampleConvertor.ConvertorFloatToShortGeneric(System.IntPtr,System.IntPtr[],System.Int32,System.Int32)">
            <summary>
            Generic convertor SHORT
            </summary>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioSampleConvertor.ConverterFloatTo24LSBGeneric(System.IntPtr,System.IntPtr[],System.Int32,System.Int32)">
            <summary>
            Generic converter 24 LSB
            </summary>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioSampleConvertor.ConverterFloatToFloatGeneric(System.IntPtr,System.IntPtr[],System.Int32,System.Int32)">
            <summary>
            Generic convertor for float
            </summary>
        </member>
        <member name="T:NAudio.Wave.Asio.AsioSampleType">
            <summary>
            ASIO Sample Type
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioSampleType.Int16MSB">
            <summary>
            Int 16 MSB
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioSampleType.Int24MSB">
            <summary>
            Int 24 MSB (used for 20 bits as well)
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioSampleType.Int32MSB">
            <summary>
            Int 32 MSB
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioSampleType.Float32MSB">
            <summary>
            IEEE 754 32 bit float
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioSampleType.Float64MSB">
            <summary>
            IEEE 754 64 bit double float
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioSampleType.Int32MSB16">
            <summary>
            32 bit data with 16 bit alignment
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioSampleType.Int32MSB18">
            <summary>
            32 bit data with 18 bit alignment
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioSampleType.Int32MSB20">
            <summary>
            32 bit data with 20 bit alignment
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioSampleType.Int32MSB24">
            <summary>
            32 bit data with 24 bit alignment
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioSampleType.Int16LSB">
            <summary>
            Int 16 LSB
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioSampleType.Int24LSB">
            <summary>
            Int 24 LSB
            used for 20 bits as well
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioSampleType.Int32LSB">
            <summary>
            Int 32 LSB
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioSampleType.Float32LSB">
            <summary>
            IEEE 754 32 bit float, as found on Intel x86 architecture
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioSampleType.Float64LSB">
            <summary>
            IEEE 754 64 bit double float, as found on Intel x86 architecture
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioSampleType.Int32LSB16">
            <summary>
            32 bit data with 16 bit alignment
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioSampleType.Int32LSB18">
            <summary>
            32 bit data with 18 bit alignment
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioSampleType.Int32LSB20">
            <summary>
            32 bit data with 20 bit alignment
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioSampleType.Int32LSB24">
            <summary>
            32 bit data with 24 bit alignment
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioSampleType.DSDInt8LSB1">
            <summary>
            DSD 1 bit data, 8 samples per byte. First sample in Least significant bit.
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioSampleType.DSDInt8MSB1">
            <summary>
            DSD 1 bit data, 8 samples per byte. First sample in Most significant bit.
            </summary>
        </member>
        <member name="F:NAudio.Wave.Asio.AsioSampleType.DSDInt8NER8">
            <summary>
            DSD 8 bit data, 1 sample per byte. No Endianness required.
            </summary>
        </member>
        <member name="T:NAudio.Wave.Asio.AsioException">
            <summary>
            ASIO common Exception.
            </summary>
        </member>
        <member name="M:NAudio.Wave.Asio.AsioException.getErrorName(NAudio.Wave.Asio.AsioError)">
            <summary>
            Gets the name of the error.
            </summary>
            <param name="error">The error.</param>
            <returns>the name of the error</returns>
        </member>
        <member name="T:NAudio.Wave.AsioAudioAvailableEventArgs">
            <summary>
            Raised when ASIO data has been recorded.
            It is important to handle this as quickly as possible as it is in the buffer callback
            </summary>
        </member>
        <member name="M:NAudio.Wave.AsioAudioAvailableEventArgs.#ctor(System.IntPtr[],System.IntPtr[],System.Int32,NAudio.Wave.Asio.AsioSampleType)">
            <summary>
            Initialises a new instance of AsioAudioAvailableEventArgs
            </summary>
            <param name="inputBuffers">Pointers to the ASIO buffers for each channel</param>
            <param name="outputBuffers">Pointers to the ASIO buffers for each channel</param>
            <param name="samplesPerBuffer">Number of samples in each buffer</param>
            <param name="asioSampleType">Audio format within each buffer</param>
        </member>
        <member name="P:NAudio.Wave.AsioAudioAvailableEventArgs.InputBuffers">
            <summary>
            Pointer to a buffer per input channel
            </summary>
        </member>
        <member name="P:NAudio.Wave.AsioAudioAvailableEventArgs.OutputBuffers">
            <summary>
            Pointer to a buffer per output channel
            Allows you to write directly to the output buffers
            If you do so, set SamplesPerBuffer = true,
            and make sure all buffers are written to with valid data
            </summary>
        </member>
        <member name="P:NAudio.Wave.AsioAudioAvailableEventArgs.WrittenToOutputBuffers">
            <summary>
            Set to true if you have written to the output buffers
            If so, AsioOut will not read from its source
            </summary>
        </member>
        <member name="P:NAudio.Wave.AsioAudioAvailableEventArgs.SamplesPerBuffer">
            <summary>
            Number of samples in each buffer
            </summary>
        </member>
        <member name="M:NAudio.Wave.AsioAudioAvailableEventArgs.GetAsInterleavedSamples(System.Single[])">
            <summary>
            Converts all the recorded audio into a buffer of 32 bit floating point samples, interleaved by channel
            </summary>
            <samples>The samples as 32 bit floating point, interleaved</samples>
        </member>
        <member name="P:NAudio.Wave.AsioAudioAvailableEventArgs.AsioSampleType">
            <summary>
            Audio format within each buffer
            Most commonly this will be one of, Int32LSB, Int16LSB, Int24LSB or Float32LSB
            </summary>
        </member>
        <member name="M:NAudio.Wave.AsioAudioAvailableEventArgs.GetAsInterleavedSamples">
            <summary>
            Gets as interleaved samples, allocating a float array
            </summary>
            <returns>The samples as 32 bit floating point values</returns>
        </member>
        <member name="T:NAudio.Wave.AsioOut">
            <summary>
            ASIO Out Player. New implementation using an internal C# binding.
            
            This implementation is only supporting Short16Bit and Float32Bit formats and is optimized 
            for 2 outputs channels .
            SampleRate is supported only if AsioDriver is supporting it
                
            This implementation is probably the first AsioDriver binding fully implemented in C#!
            
            Original Contributor: Mark Heath 
            New Contributor to C# binding : Alexandre Mutel - email: alexandre_mutel at yahoo.fr
            </summary>
        </member>
        <member name="E:NAudio.Wave.AsioOut.PlaybackStopped">
            <summary>
            Playback Stopped
            </summary>
        </member>
        <member name="E:NAudio.Wave.AsioOut.AudioAvailable">
            <summary>
            When recording, fires whenever recorded audio is available
            </summary>
        </member>
        <member name="E:NAudio.Wave.AsioOut.DriverResetRequest">
            <summary>
            Occurs when the driver settings are changed by the user, e.g. in the control panel.
            </summary>
        </member>
        <member name="M:NAudio.Wave.AsioOut.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:NAudio.Wave.AsioOut"/> class with the first 
            available ASIO Driver.
            </summary>
        </member>
        <member name="M:NAudio.Wave.AsioOut.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:NAudio.Wave.AsioOut"/> class with the driver name.
            </summary>
            <param name="driverName">Name of the device.</param>
        </member>
        <member name="M:NAudio.Wave.AsioOut.#ctor(System.Int32)">
            <summary>
            Opens an ASIO output device
            </summary>
            <param name="driverIndex">Device number (zero based)</param>
        </member>
        <member name="M:NAudio.Wave.AsioOut.Finalize">
            <summary>
            Releases unmanaged resources and performs other cleanup operations before the
            <see cref="T:NAudio.Wave.AsioOut"/> is reclaimed by garbage collection.
            </summary>
        </member>
        <member name="M:NAudio.Wave.AsioOut.Dispose">
            <summary>
            Dispose
            </summary>
        </member>
        <member name="M:NAudio.Wave.AsioOut.GetDriverNames">
            <summary>
            Gets the names of the installed ASIO Driver.
            </summary>
            <returns>an array of driver names</returns>
        </member>
        <member name="M:NAudio.Wave.AsioOut.isSupported">
            <summary>
            Determines whether ASIO is supported.
            </summary>
            <returns>
                <c>true</c> if ASIO is supported; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:NAudio.Wave.AsioOut.IsSampleRateSupported(System.Int32)">
            <summary>
            Determines whether this driver supports the specified sample rate.
            </summary>
            <param name="sampleRate">The samplerate to check.</param>
            <returns>
              <c>true</c> if the specified sample rate is supported otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:NAudio.Wave.AsioOut.InitFromName(System.String)">
            <summary>
            Inits the driver from the asio driver name.
            </summary>
            <param name="driverName">Name of the driver.</param>
        </member>
        <member name="M:NAudio.Wave.AsioOut.ReleaseDriver(NAudio.Wave.Asio.AsioDriver)">
            <summary>
            Release driver
            </summary>
        </member>
        <member name="M:NAudio.Wave.AsioOut.ShowControlPanel">
            <summary>
            Shows the control panel
            </summary>
        </member>
        <member name="M:NAudio.Wave.AsioOut.Play">
            <summary>
            Starts playback
            </summary>
        </member>
        <member name="M:NAudio.Wave.AsioOut.Stop">
            <summary>
            Stops playback
            </summary>
        </member>
        <member name="M:NAudio.Wave.AsioOut.Pause">
            <summary>
            Pauses playback
            </summary>
        </member>
        <member name="M:NAudio.Wave.AsioOut.Init(NAudio.Wave.IWaveProvider)">
            <summary>
            Initialises to play
            </summary>
            <param name="waveProvider">Source wave provider</param>
        </member>
        <member name="M:NAudio.Wave.AsioOut.InitRecordAndPlayback(NAudio.Wave.IWaveProvider,System.Int32,System.Int32)">
            <summary>
            Initialises to play, with optional recording
            </summary>
            <param name="waveProvider">Source wave provider - set to null for record only</param>
            <param name="recordChannels">Number of channels to record</param>
            <param name="recordOnlySampleRate">Specify sample rate here if only recording, ignored otherwise</param>
        </member>
        <member name="M:NAudio.Wave.AsioOut.driver_BufferUpdate(System.IntPtr[],System.IntPtr[])">
            <summary>
            driver buffer update callback to fill the wave buffer.
            </summary>
            <param name="inputChannels">The input channels.</param>
            <param name="outputChannels">The output channels.</param>
        </member>
        <member name="P:NAudio.Wave.AsioOut.PlaybackLatency">
            <summary>
            Gets the latency (in samples) of the playback driver
            </summary>
        </member>
        <member name="P:NAudio.Wave.AsioOut.AutoStop">
            <summary>
            Automatically stop when the end of the input stream is reached
            Disable this if auto-stop is causing hanging issues
            </summary>
        </member>
        <member name="P:NAudio.Wave.AsioOut.HasReachedEnd">
            <summary>
            A flag to let you know that we have reached the end of the input file
            Useful if AutoStop is set to false
            You can monitor this yourself and call Stop when it is true
            </summary>
        </member>
        <member name="P:NAudio.Wave.AsioOut.PlaybackState">
            <summary>
            Playback State
            </summary>
        </member>
        <member name="P:NAudio.Wave.AsioOut.DriverName">
            <summary>
            Driver Name
            </summary>
        </member>
        <member name="P:NAudio.Wave.AsioOut.NumberOfOutputChannels">
            <summary>
            The number of output channels we are currently using for playback
            (Must be less than or equal to DriverOutputChannelCount)
            </summary>
        </member>
        <member name="P:NAudio.Wave.AsioOut.NumberOfInputChannels">
            <summary>
            The number of input channels we are currently recording from
            (Must be less than or equal to DriverInputChannelCount)
            </summary>
        </member>
        <member name="P:NAudio.Wave.AsioOut.DriverInputChannelCount">
            <summary>
            The maximum number of input channels this ASIO driver supports
            </summary>
        </member>
        <member name="P:NAudio.Wave.AsioOut.DriverOutputChannelCount">
            <summary>
            The maximum number of output channels this ASIO driver supports
            </summary>
        </member>
        <member name="P:NAudio.Wave.AsioOut.FramesPerBuffer">
            <summary>
            The number of samples per channel, per buffer.
            </summary>
        </member>
        <member name="P:NAudio.Wave.AsioOut.ChannelOffset">
            <summary>
            By default the first channel on the input WaveProvider is sent to the first ASIO output.
            This option sends it to the specified channel number.
            Warning: make sure you don't set it higher than the number of available output channels - 
            the number of source channels.
            n.b. Future NAudio may modify this
            </summary>
        </member>
        <member name="P:NAudio.Wave.AsioOut.InputChannelOffset">
            <summary>
            Input channel offset (used when recording), allowing you to choose to record from just one
            specific input rather than them all
            </summary>
        </member>
        <member name="P:NAudio.Wave.AsioOut.Volume">
            <summary>
            Sets the volume (1.0 is unity gain)
            Not supported for ASIO Out. Set the volume on the input stream instead
            </summary>
        </member>
        <member name="P:NAudio.Wave.AsioOut.OutputWaveFormat">
            <inheritdoc/>
        </member>
        <member name="M:NAudio.Wave.AsioOut.AsioInputChannelName(System.Int32)">
            <summary>
            Get the input channel name
            </summary>
            <param name="channel">channel index (zero based)</param>
            <returns>channel name</returns>
        </member>
        <member name="M:NAudio.Wave.AsioOut.AsioOutputChannelName(System.Int32)">
            <summary>
            Get the output channel name
            </summary>
            <param name="channel">channel index (zero based)</param>
            <returns>channel name</returns>
        </member>
    </members>
</doc>
